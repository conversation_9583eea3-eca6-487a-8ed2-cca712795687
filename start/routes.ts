/*
|--------------------------------------------------------------------------
| Routes
|--------------------------------------------------------------------------
|
| This file is dedicated for defining HTTP routes. A single file is enough
| for majority of projects, however you can define routes in different
| files and just make sure to import them inside this file. For example
|
| Define routes in following two files
| ├── start/routes/cart.ts
| ├── start/routes/customer.ts
|
| and then import them inside `start/routes.ts` as follows
|
| import './routes/cart'
| import './routes/customer'
|
*/

import Route from "@ioc:Adonis/Core/Route";

Route.get("/", async () => {
  return { hello: "world" };
});

Route.post("/api/v1/export", "DatasetsController.export");
Route.post("/api/v1/import", "DatasetsController.import");
Route.get("/api/v1/list", "DatasetsController.getList");
Route.get("/api/v1/backups", "DatasetsController.getBackups");
Route.post("/api/v1/backups", "DatasetsController.uploadBackup");

Route.get("/api/v1/feeds/reviews", "FeedsController.reviews");
Route.get("/api/v1/feeds/shop-products", "FeedsController.shopProducts");
