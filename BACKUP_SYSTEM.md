# Sanity Dataset Backup System

This document describes the automated daily backup system for Sanity datasets implemented in this AdonisJS application.

## Overview

The backup system provides:
- **Automated daily backups** of Sanity datasets
- **Cloud storage integration** with DigitalOcean Spaces
- **Email notifications** when backups complete
- **Manual backup commands** for on-demand backups
- **Configurable backup settings** via environment variables

## Components

### 1. BackupService (`app/Services/BackupService.ts`)
A service class that handles the core backup functionality:
- Exports Sanity datasets using the Sanity CLI
- Uploads backup files to cloud storage
- Sends email notifications
- Manages local file cleanup

### 2. DailyBackup Task (`app/Tasks/DailyBackup.ts`)
A scheduled task that runs daily at 2:00 AM to automatically backup configured datasets.

### 3. BackupDataset Command (`commands/BackupDataset.ts`)
A manual command for triggering backups on-demand.

## Configuration

### Environment Variables

Add these variables to your `.env` file:

```env
# Sanity Configuration
SANITY_PROJECT_ID=your_project_id
SANITY_PERSONAL_TOKEN=your_personal_token

# Cloud Storage Configuration (DigitalOcean Spaces)
BUCKET=your_bucket_name
REGION=your_region
SPACES_KEY=your_spaces_key
SPACES_SECRET=your_spaces_secret

# Email Configuration
SENDGRID_API_KEY=your_sendgrid_api_key
DEFAULT_SENDGRID_MAIL=<EMAIL>

# Backup Configuration
# Supports patterns with * wildcard and exact names
BACKUP_DATASETS=jp_*
BACKUP_NOTIFICATION_EMAILS=<EMAIL>,<EMAIL>

# Security
SECRET=your_secret_key
```

### Configuration Details

- **BACKUP_DATASETS**: Supports both patterns and exact names (default: "jp_*")
  - **Patterns**: Use `*` wildcard to match multiple datasets
    - `jp_*` - matches all datasets starting with "jp_"
    - `*_2025*` - matches datasets containing "_2025"
    - `jp_*,staging_*` - multiple patterns
  - **Exact names**: `jp_20250101,jp_20250102`
  - **Mixed**: `jp_*,production,staging_*`
- **BACKUP_NOTIFICATION_EMAILS**: Comma-separated list of email addresses to notify (default: "<EMAIL>,<EMAIL>")

## Usage

### Starting the Scheduler

To enable daily automated backups, start the scheduler:

```bash
node ace scheduler:run
```

This should be run as a background process in production. Consider using PM2 or similar process managers:

```bash
pm2 start "node ace scheduler:run" --name "sanity-backup-scheduler"
```

### Manual Backup Commands

#### Backup a specific dataset:
```bash
node ace backup:dataset production
```

#### Backup with custom options:
```bash
# Backup without assets
node ace backup:dataset production --no-assets

# Backup without drafts
node ace backup:dataset production --no-drafts

# Keep local file after upload
node ace backup:dataset production --keep-local

# Custom notification emails
node ace backup:dataset production --emails="<EMAIL>,<EMAIL>"
```

#### List available datasets:
```bash
node ace backup:dataset
```

#### Check which datasets will be backed up:
```bash
# Using current BACKUP_DATASETS configuration
node ace list:backup_datasets

# Test a specific pattern
node ace list:backup_datasets --pattern="jp_*"

# Test multiple patterns
node ace list:backup_datasets --pattern="jp_*,staging_*"
```

### Backup Features

- **Assets**: Include/exclude asset files (images, documents, etc.)
- **Drafts**: Include/exclude draft documents
- **Notifications**: Email notifications with backup status and download links
- **File Management**: Automatic cleanup of local backup files after upload
- **Error Handling**: Comprehensive error logging and reporting

## Backup File Format

Backup files are created in the format:
```
{dataset}_{timestamp}.tar.gz
```

Example: `production_2024-01-15T02:00:00.000Z.tar.gz`

## Monitoring

### Logs

The backup system provides detailed logging:
- Task execution logs
- Export progress
- Upload status
- Error details

### Email Notifications

Email notifications include:
- Dataset name
- Backup timestamp
- File name
- Download URL
- Success/failure status

## Troubleshooting

### Common Issues

1. **Sanity CLI not found**
   - Ensure Sanity CLI is installed globally: `npm install -g @sanity/cli`
   - Verify SANITY_PERSONAL_TOKEN is set correctly

2. **Upload failures**
   - Check DigitalOcean Spaces credentials
   - Verify bucket permissions
   - Ensure sufficient storage space

3. **Email notifications not working**
   - Verify SENDGRID_API_KEY is valid
   - Check DEFAULT_SENDGRID_MAIL is configured
   - Ensure sender email is verified in SendGrid

4. **Scheduler not running**
   - Verify adonis5-scheduler is properly installed
   - Check that the scheduler process is running
   - Review application logs for errors

### Debug Commands

Check available commands:
```bash
node ace list
```

Test backup service manually:
```bash
node ace backup:dataset your-dataset-name --keep-local
```

## Security Considerations

- Store sensitive environment variables securely
- Use least-privilege access for cloud storage
- Regularly rotate API tokens and keys
- Monitor backup access logs
- Consider encrypting backup files for sensitive data

## Maintenance

### Regular Tasks

1. **Monitor backup success rates**
2. **Review storage usage and costs**
3. **Test backup restoration procedures**
4. **Update dependencies regularly**
5. **Rotate API tokens periodically**

### Backup Retention

Consider implementing backup retention policies:
- Keep daily backups for 30 days
- Keep weekly backups for 3 months
- Keep monthly backups for 1 year

This can be implemented by extending the BackupService with cleanup functionality.
