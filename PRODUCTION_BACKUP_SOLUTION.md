# Production Dataset Backup Solution

## Your Situation
You have dynamic dataset names where:
- `jp_playground` = development/testing
- `jp_20250326` = older production
- `jp_20250413` = current production (usually 2nd latest)
- `jp_20250502` = staging (usually latest)

**Goal**: Backup only the production dataset daily (usually the 2nd latest)

## Recommended Solutions

### Solution 1: Manual Configuration (Recommended)
This is the most reliable approach for production environments.

**Setup:**
```env
# Update this when you create new datasets
BACKUP_DATASETS=jp_20250413
BACKUP_NOTIFICATION_EMAILS=<EMAIL>
```

**Pros:**
- ✅ Precise control over what gets backed up
- ✅ No risk of backing up wrong datasets
- ✅ Works reliably

**Cons:**
- ❌ Requires manual updates when you create new datasets

**When to update:**
- When you create a new production dataset
- When production dataset changes

### Solution 2: Pattern-Based (Semi-Automatic)
Use patterns to automatically include datasets, but exclude playground.

**Setup:**
```env
# Backup all jp_ datasets from 2025 (excludes playground)
BACKUP_DATASETS=jp_2025*
BACKUP_NOTIFICATION_EMAILS=<EMAIL>
```

**Pros:**
- ✅ Automatically includes new datasets
- ✅ Excludes playground datasets

**Cons:**
- ❌ Will backup both production AND staging
- ❌ Need to update pattern for new years

### Solution 3: All JP Datasets
Backup everything and sort it out later.

**Setup:**
```env
# Backup all jp_ datasets
BACKUP_DATASETS=jp_*
BACKUP_NOTIFICATION_EMAILS=<EMAIL>
```

**Pros:**
- ✅ Never miss a dataset
- ✅ Fully automatic

**Cons:**
- ❌ Backs up staging, playground, and everything else
- ❌ Higher storage costs
- ❌ More backup noise

## Implementation Steps

### Step 1: Choose Your Strategy
I recommend **Solution 1 (Manual Configuration)** for production because:
- You have full control
- No surprises
- Most reliable

### Step 2: Update Your .env File
```env
# Replace with your current production dataset
BACKUP_DATASETS=jp_20250413

# Your notification email
BACKUP_NOTIFICATION_EMAILS=<EMAIL>
```

### Step 3: Test the Configuration
```bash
# Check what will be backed up
node ace list:backup_datasets

# Test a manual backup
node ace backup:dataset jp_20250413 --keep-local
```

### Step 4: Start the Scheduler
```bash
# For development/testing
node ace scheduler:run

# For production (using PM2)
pm2 start "node ace scheduler:run" --name "sanity-backup-scheduler"
```

## Maintenance Process

### When You Create New Datasets

1. **Identify the new production dataset** (usually the 2nd latest)
2. **Update your .env file:**
   ```env
   BACKUP_DATASETS=jp_20250515  # New production dataset
   ```
3. **Restart the scheduler** (if using PM2):
   ```bash
   pm2 restart sanity-backup-scheduler
   ```

### Automation Script (Optional)
Create a script to help identify the production dataset:

```bash
#!/bin/bash
# get_production_dataset.sh

echo "Available JP datasets:"
SANITY_AUTH_TOKEN=$SANITY_PERSONAL_TOKEN sanity dataset list | grep "jp_" | grep -v playground | sort -r

echo ""
echo "Suggested production dataset (2nd latest):"
SANITY_AUTH_TOKEN=$SANITY_PERSONAL_TOKEN sanity dataset list | grep "jp_" | grep -v playground | sort -r | sed -n '2p'
```

## Testing Commands

```bash
# List all available datasets
node ace backup:dataset

# Check current backup configuration
node ace list:backup_datasets

# Test different patterns
node ace list:backup_datasets --pattern="jp_2025*"
node ace list:backup_datasets --pattern="jp_*"

# Manual backup with specific dataset
node ace backup:dataset jp_20250413 --keep-local

# Check backup files in cloud storage
# (This will list files in your DigitalOcean Spaces bucket)
```

## Monitoring

### Daily Checks
- ✅ Backup completion emails
- ✅ Files appearing in cloud storage
- ✅ Scheduler process running

### Weekly Checks
- ✅ Verify backup files can be downloaded
- ✅ Check storage usage
- ✅ Review backup logs

### When Creating New Datasets
- ✅ Update BACKUP_DATASETS configuration
- ✅ Test backup with new dataset
- ✅ Verify old production dataset is no longer being backed up

## Quick Reference

| Task | Command |
|------|---------|
| List datasets | `node ace backup:dataset` |
| Check backup config | `node ace list:backup_datasets` |
| Manual backup | `node ace backup:dataset <dataset-name>` |
| Start scheduler | `node ace scheduler:run` |
| Test pattern | `node ace list:backup_datasets --pattern="<pattern>"` |

## Summary

For your use case with dynamic dataset names where production is usually the 2nd latest, I recommend:

1. **Use manual configuration** (`BACKUP_DATASETS=jp_20250413`)
2. **Update the configuration** when you create new datasets
3. **Test the backup** before relying on it
4. **Monitor backup completion** via email notifications

This approach gives you the most control and reliability for production backups.
