import { BaseTask, CronTimeV2 } from 'adonis5-scheduler/build/src/Scheduler/Task'
import BackupService from '../Services/BackupService'
import Env from '@ioc:Adonis/Core/Env'

export default class DailyBackup extends BaseTask {
  public static get schedule() {
    // Run daily at 2:00 AM
    return '0 2 * * *'
  }

  /**
   * Set enable use .lock file for block run retry task
   * Lock file save to `build/tmp/adonis5-scheduler/locks/your-class-name`
   */
  public static get useLock() {
    return true // Enable lock to prevent concurrent backups
  }

  public async handle() {
    this.logger.info('Starting daily Sanity dataset backup...')

    const backupService = new BackupService()

    try {
      // Get datasets to backup from environment variable or default to 'production'
      const datasetsToBackup = this.getDatasetsToBackup()

      this.logger.info(`Backing up datasets: ${datasetsToBackup.join(', ')}`)

      // Backup each dataset
      for (const dataset of datasetsToBackup) {
        this.logger.info(`Starting backup for dataset: ${dataset}`)

        const result = await backupService.exportDataset({
          dataset,
          includeAssets: true,
          includeDrafts: true,
          notificationEmails: this.getNotificationEmails(),
          cleanupLocalFile: true,
        })

        if (result.success) {
          this.logger.info(`✅ Backup completed for dataset: ${dataset}`)
          this.logger.info(`   File: ${result.fileName}`)
          this.logger.info(`   Upload URL: ${result.uploadUrl}`)
        } else {
          this.logger.error(`❌ Backup failed for dataset: ${dataset}`)
          this.logger.error(`   Error: ${result.error}`)
        }

        // Add a small delay between backups to avoid overwhelming the system
        if (datasetsToBackup.length > 1) {
          await new Promise(resolve => setTimeout(resolve, 5000))
        }
      }

      this.logger.info('Daily backup process completed')

    } catch (error) {
      this.logger.error('Daily backup process failed:', error)
      throw error
    }
  }

  /**
   * Get the list of datasets to backup from environment variable
   */
  private getDatasetsToBackup(): string[] {
    const datasetsEnv = Env.get('BACKUP_DATASETS', 'production')
    return datasetsEnv.split(',').map((dataset: string) => dataset.trim())
  }

  /**
   * Get notification email addresses from environment variable
   */
  private getNotificationEmails(): string[] {
    const emailsEnv = Env.get('BACKUP_NOTIFICATION_EMAILS', '<EMAIL>,<EMAIL>')
    return emailsEnv.split(',').map((email: string) => email.trim())
  }
}
