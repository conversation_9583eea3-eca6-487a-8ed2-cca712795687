import { BaseTask, CronTimeV2 } from 'adonis5-scheduler/build/src/Scheduler/Task'
import BackupService from '../Services/BackupService'
import Env from '@ioc:Adonis/Core/Env'

export default class DailyBackup extends BaseTask {
  public static get schedule() {
    // Run daily at 2:00 AM
    return '0 2 * * *'
  }

  /**
   * Set enable use .lock file for block run retry task
   * Lock file save to `build/tmp/adonis5-scheduler/locks/your-class-name`
   */
  public static get useLock() {
    return true // Enable lock to prevent concurrent backups
  }

  public async handle() {
    this.logger.info('Starting daily Sanity dataset backup...')

    const backupService = new BackupService()

    try {
      // Get datasets to backup from environment variable or default to 'jp_*'
      const datasetsToBackup = await this.getDatasetsToBackup()

      if (datasetsToBackup.length === 0) {
        this.logger.warn('No datasets found matching the backup patterns')
        return
      }

      this.logger.info(`Backing up datasets: ${datasetsToBackup.join(', ')}`)

      // Backup each dataset
      for (const dataset of datasetsToBackup) {
        this.logger.info(`Starting backup for dataset: ${dataset}`)

        const result = await backupService.exportDataset({
          dataset,
          includeAssets: true,
          includeDrafts: true,
          notificationEmails: this.getNotificationEmails(),
          cleanupLocalFile: true,
        })

        if (result.success) {
          this.logger.info(`✅ Backup completed for dataset: ${dataset}`)
          this.logger.info(`   File: ${result.fileName}`)
          this.logger.info(`   Upload URL: ${result.uploadUrl}`)
        } else {
          this.logger.error(`❌ Backup failed for dataset: ${dataset}`)
          this.logger.error(`   Error: ${result.error}`)
        }

        // Add a small delay between backups to avoid overwhelming the system
        if (datasetsToBackup.length > 1) {
          await new Promise(resolve => setTimeout(resolve, 5000))
        }
      }

      this.logger.info('Daily backup process completed')

    } catch (error) {
      this.logger.error('Daily backup process failed:', error)
      throw error
    }
  }

  /**
   * Get the list of datasets to backup from environment variable
   * Supports patterns, smart selection, and explicit names
   */
  private async getDatasetsToBackup(): Promise<string[]> {
    const datasetsEnv = Env.get('BACKUP_DATASETS', 'production:jp_')
    const patterns = datasetsEnv.split(',').map((pattern: string) => pattern.trim())

    const backupService = new BackupService()
    const datasetsToBackup: string[] = []

    for (const pattern of patterns) {
      if (pattern.startsWith('production:')) {
        // Smart production selection: production:prefix
        const prefix = pattern.replace('production:', '') || 'jp_'
        const productionDataset = await backupService.getProductionDataset({
          prefix: prefix,
          excludeLatest: true,
          excludePlayground: true
        })

        if (productionDataset) {
          datasetsToBackup.push(productionDataset)
          this.logger.info(`🎯 Production dataset selected: ${productionDataset}`)
        } else {
          this.logger.warn(`⚠️  No production dataset found for prefix: ${prefix}`)
        }

      } else if (pattern.startsWith('staging:')) {
        // Smart staging selection: staging:prefix
        const prefix = pattern.replace('staging:', '') || 'jp_'
        const stagingDataset = await backupService.getStagingDataset({
          prefix: prefix,
          excludePlayground: true
        })

        if (stagingDataset) {
          datasetsToBackup.push(stagingDataset)
          this.logger.info(`🚀 Staging dataset selected: ${stagingDataset}`)
        } else {
          this.logger.warn(`⚠️  No staging dataset found for prefix: ${prefix}`)
        }

      } else if (pattern.includes('*')) {
        // Pattern matching (existing functionality)
        const allDatasets = await backupService.getAvailableDatasets()
        const regex = new RegExp('^' + pattern.replace(/\*/g, '.*') + '$')
        const matchingDatasets = allDatasets.filter(dataset => regex.test(dataset))
        datasetsToBackup.push(...matchingDatasets)

      } else {
        // Exact match (existing functionality)
        const allDatasets = await backupService.getAvailableDatasets()
        if (allDatasets.includes(pattern)) {
          datasetsToBackup.push(pattern)
        }
      }
    }

    // Remove duplicates
    return [...new Set(datasetsToBackup)]
  }

  /**
   * Get notification email addresses from environment variable
   */
  private getNotificationEmails(): string[] {
    const emailsEnv = Env.get('BACKUP_NOTIFICATION_EMAILS', '<EMAIL>,<EMAIL>')
    return emailsEnv.split(',').map((email: string) => email.trim())
  }
}
