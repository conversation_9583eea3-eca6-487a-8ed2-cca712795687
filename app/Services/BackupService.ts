import { spawn } from "child_process";
import { DateTime } from "luxon";
import fs from "fs";
import sgMail, { MailDataRequired } from "@sendgrid/mail";
import { uploadFileToSpacesBucket } from "../Controllers/Http/FilesController";

export interface BackupOptions {
  dataset: string;
  includeAssets?: boolean;
  includeDrafts?: boolean;
  notificationEmails?: string[];
  cleanupLocalFile?: boolean;
}

export interface BackupResult {
  success: boolean;
  fileName?: string;
  uploadUrl?: string;
  error?: string;
}

export default class BackupService {
  private sendgridKey: string;

  constructor() {
    this.sendgridKey = process.env.SENDGRID_API_KEY!;
    if (this.sendgridKey) {
      sgMail.setApiKey(this.sendgridKey);
    }
  }

  /**
   * Export a Sanity dataset and upload it to cloud storage
   */
  public async exportDataset(options: BackupOptions): Promise<BackupResult> {
    const {
      dataset,
      includeAssets = true,
      includeDrafts = true,
      notificationEmails = ["<EMAIL>", "<EMAIL>"],
      cleanupLocalFile = true,
    } = options;

    const now = DateTime.now();
    const fileName = `${dataset}_${now.toISO()}.tar.gz`;

    try {
      console.log(`Starting backup for dataset: ${dataset}`);

      // Build the sanity export command
      let exportCommand = `SANITY_AUTH_TOKEN=${process.env.SANITY_PERSONAL_TOKEN!} sanity dataset export ${dataset} ${fileName}`;
      
      if (!includeAssets) {
        exportCommand += " --no-assets";
      }
      
      if (!includeDrafts) {
        exportCommand += " --no-drafts";
      }

      // Execute the export
      const exportResult = await this.executeSanityExport(exportCommand, fileName);
      
      if (!exportResult.success) {
        return exportResult;
      }

      // Upload to cloud storage
      const uploadResult = await this.uploadBackupFile(fileName);
      
      if (!uploadResult.success) {
        return uploadResult;
      }

      // Send notification email
      if (this.sendgridKey && notificationEmails.length > 0) {
        await this.sendNotificationEmail(
          notificationEmails,
          dataset,
          fileName,
          uploadResult.uploadUrl!
        );
      }

      // Cleanup local file if requested
      if (cleanupLocalFile && fs.existsSync(fileName)) {
        fs.unlinkSync(fileName);
        console.log(`Cleaned up local file: ${fileName}`);
      }

      return {
        success: true,
        fileName,
        uploadUrl: uploadResult.uploadUrl,
      };
    } catch (error) {
      console.error("Backup failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
      };
    }
  }

  /**
   * Execute the Sanity export command
   */
  private async executeSanityExport(
    command: string,
    fileName: string
  ): Promise<BackupResult> {
    return new Promise((resolve) => {
      console.log(`Executing: ${command}`);
      
      const child = spawn(command, [], { shell: true, stdio: "inherit" });

      child.stdout?.on("data", (data) => {
        console.log("stdout:", `${data}`);
      });

      child.stderr?.on("data", (data) => {
        console.log("stderr:", `${data}`);
      });

      child.on("close", async (code) => {
        console.log(`Export process closed with code: ${code}`);

        if (code !== 0) {
          resolve({
            success: false,
            error: `Export process failed with exit code ${code}`,
          });
          return;
        }

        // Check if file was created
        const fileExists = fs.existsSync(fileName);
        if (!fileExists) {
          resolve({
            success: false,
            error: `Export file ${fileName} was not created`,
          });
          return;
        }

        resolve({
          success: true,
          fileName,
        });
      });

      child.on("error", (error) => {
        console.error("Export process error:", error);
        resolve({
          success: false,
          error: `Export process error: ${error.message}`,
        });
      });
    });
  }

  /**
   * Upload the backup file to cloud storage
   */
  private async uploadBackupFile(fileName: string): Promise<BackupResult> {
    try {
      if (!fs.existsSync(fileName)) {
        return {
          success: false,
          error: `File ${fileName} does not exist`,
        };
      }

      console.log(`Uploading ${fileName} to cloud storage...`);
      
      const uploadResponse = await uploadFileToSpacesBucket(
        fileName,
        process.env.BUCKET!,
        fileName,
        "application/gzip"
      );

      console.log("Upload successful:", uploadResponse);

      return {
        success: true,
        fileName,
        uploadUrl: uploadResponse.url,
      };
    } catch (error) {
      console.error("Upload failed:", error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Upload failed",
      };
    }
  }

  /**
   * Send notification email about the backup
   */
  private async sendNotificationEmail(
    emails: string[],
    dataset: string,
    fileName: string,
    uploadUrl: string
  ): Promise<void> {
    try {
      const message: MailDataRequired = {
        to: emails,
        from: process.env.DEFAULT_SENDGRID_MAIL!,
        subject: `Sanity Backup Complete - ${dataset}`,
        html: `
          <h2>Sanity Dataset Backup Complete</h2>
          <p><strong>Dataset:</strong> ${dataset}</p>
          <p><strong>File:</strong> ${fileName}</p>
          <p><strong>Backup Time:</strong> ${DateTime.now().toLocaleString()}</p>
          <p><strong>Status:</strong> Successfully uploaded to cloud storage</p>
          <p><strong>Download URL:</strong> <a href="${uploadUrl}">Download Backup</a></p>
        `,
      };

      await sgMail.sendMultiple(message);
      console.log(`Notification email sent to: ${emails.join(", ")}`);
    } catch (error) {
      console.error("Failed to send notification email:", error);
      // Don't throw error here as backup was successful
    }
  }

  /**
   * Get list of available datasets
   */
  public async getAvailableDatasets(): Promise<string[]> {
    try {
      const { exec } = require("child_process");
      const util = require("util");
      const awaitExec = util.promisify(exec);

      const { stdout, stderr } = await awaitExec(
        `SANITY_AUTH_TOKEN=${process.env.SANITY_PERSONAL_TOKEN!} sanity dataset list`
      );

      if (stderr) {
        console.log(`stderr: ${stderr}`);
      }

      if (stdout) {
        const { sift } = require("radash");
        return sift(stdout.split("\n"));
      }

      return [];
    } catch (error) {
      console.error("Failed to get dataset list:", error);
      return [];
    }
  }
}
