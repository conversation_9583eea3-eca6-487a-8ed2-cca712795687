import type { HttpContextContract } from "@ioc:Adonis/Core/HttpContext";
import { spawn, exec } from "child_process";
import { DateTime } from "luxon";
import { sift } from "radash";
import util from "util";
import {
  getFilesFromBucket,
  getSignedUrl,
  uploadFileToSpacesBucket,
} from "./FilesController";
import fs from "fs";
// import exportDataset from "@sanity/export";
// import { createClient } from "@sanity/client";
import { schema } from "@ioc:Adonis/Core/Validator";
import sgMail, { MailDataRequired } from "@sendgrid/mail";

const awaitExec = util.promisify(exec);
// const awaitReadFile = util.promisify(fs.readFile);
const SECRET = process.env.SECRET;
const sendgridKey = process.env.SENDGRID_API_KEY!;
export default class DatasetsController {
  public async export({ response, request }: HttpContextContract) {
    console.log(request.all());

    const validateDataSchema = schema.create({
      dataset: schema.string(),
      secret: schema.string(),
    });

    const validateData = await request.validate({
      schema: validateDataSchema,
    });

    if (typeof SECRET !== "undefined" && validateData.secret !== SECRET) {
      return response.status(401).send({
        message: "Unauthorized",
      });
    }

    const dataset = validateData.dataset;
    const now = DateTime.now();
    // const client = createClient({
    //   projectId: process.env.SANITY_PROJECT_ID,
    //   dataset: dataset,
    //   useCdn: false,
    //   apiVersion: "2023-05-03",
    //   token: process.env.SANITY_TOKEN,
    // });

    try {
      // await exportDataset({
      //   // Instance of @sanity/client configured to correct project ID and dataset
      //   client: client,

      //   // Name of dataset to export
      //   dataset: dataset,

      //   // Path to write tar.gz-archive file to, or `-` for stdout
      //   outputPath: `${dataset}_${now.toISO()}.tar.gz`,

      //   // Whether or not to export assets. Note that this operation is currently slightly lossy;
      //   // metadata stored on the asset document itself (original filename, for instance) might be lost
      //   // Default: `true`
      //   // assets: false,

      //   // Exports documents only, without downloading or rewriting asset references
      //   // Default: `false`
      //   // raw: true,

      //   // Whether or not to export drafts
      //   // Default: `true`
      //   drafts: true,

      //   // Export only given document types (`_type`)
      //   // Optional, default: all types
      //   // types: ["products", "shops"],

      //   // Run 12 concurrent asset downloads
      //   assetConcurrency: 12,
      // });

      // // if no catch then we are done with export
      // const uploadResponse = await uploadFileToSpacesBucket(
      //   `${dataset}_${now.toISO()}.tar.gz`,
      //   process.env.BUCKET!,
      //   `${dataset}_${now.toISO()}.tar.gz`,
      //   `application/gzip`
      // );

      // console.log(uploadResponse);

      // return response.status(200).send({ success: true });

      const child = await spawn(
        `SANITY_AUTH_TOKEN=${process.env.SANITY_PERSONAL_TOKEN!} sanity dataset export ${dataset} ${dataset}_${now.toISO()}.tar.gz`,
        [],
        { shell: true, stdio: "inherit" }
      );

      child.stdout?.on("data", (d) => {
        console.log("stdout:", `${d}`);
      });

      child.stderr?.on("data", function (data) {
        console.log("stderr: " + `${data}`);
      });

      child.on("close", async function (code) {
        console.log("closing code: " + code);

        // send success notification

        // upload to spaces
        const fileExist = await fs.existsSync(
          `${dataset}_${now.toISO()}.tar.gz`
        );

        if (fileExist) {
          const uploadResponse = await uploadFileToSpacesBucket(
            `${dataset}_${now.toISO()}.tar.gz`,
            process.env.BUCKET!,
            `${dataset}_${now.toISO()}.tar.gz`,
            `application/gzip`
          );

          console.log(uploadResponse);
        } else {
          console.log("no file");
        }

        const message: MailDataRequired = {
          to: ["<EMAIL>", "<EMAIL>"],
          from: process.env.DEFAULT_SENDGRID_MAIL!,
          subject: "Sanity Export",
          html: "Export done",
        };

        sgMail.setApiKey(sendgridKey);
        sgMail.sendMultiple(message);
      });

      return response.status(200).send({ success: true });
    } catch (error) {
      console.log(error);
      return response.status(500).send({ success: false, error });
    }
  }

  public async import({ request, response }: HttpContextContract) {
    console.log(request.all());

    const validateDataSchema = schema.create({
      fileKey: schema.string(),
      name: schema.string(),
      secret: schema.string(),
    });

    const validateData = await request.validate({
      schema: validateDataSchema,
    });

    if (typeof SECRET !== "undefined" && validateData.secret !== SECRET) {
      return response.status(401).send({
        message: "Unauthorized",
      });
    }

    // get dataset from spaces
    const url = await getSignedUrl(validateData.fileKey);
    console.log(url);
    // const encodeUrl = encodeURIComponent(url);
    const dataset = validateData.name;

    try {
      console.log(`SANITY_AUTH_TOKEN=${process.env.SANITY_PERSONAL_TOKEN!} sanity dataset import '${url}' ${dataset}`);
      const child = spawn(`SANITY_AUTH_TOKEN=${process.env.SANITY_PERSONAL_TOKEN!} sanity dataset import '${url}' ${dataset}`, [], {
        shell: true,
        // stdio: "inherit",
      });

      child.stdout?.on("data", (data) => {
        console.log("stdout:", `${data}`);

        if (data.toString().includes(`Dataset "${dataset}" does not exist`)) {
          console.log("tt");
          child.stdin?.write("Y");
          child.stdin?.end();
        }
      });

      child.stderr?.on("data", function (data) {
        console.log("stderr: " + `${data}`);

        if (data.toString().includes("Payment required - Quota exceeded")) {
          // send error to frontend
        }
      });

      child.on("close", function (code) {
        console.log("closing code: " + code);

        // send success notification
        const message: MailDataRequired = {
          to: ["<EMAIL>", "<EMAIL>"],
          from: process.env.DEFAULT_SENDGRID_MAIL!,
          subject: "Sanity Import",
          html: "Import done",
        };

        sgMail.setApiKey(sendgridKey);
        sgMail.sendMultiple(message);
      });

      return response.status(200).send({ success: true });
    } catch (error) {
      console.log(error);
      return response.status(500).send({ success: false, error });
    }
  }

  public async getList({ response }: HttpContextContract) {
    try {
      let datasets: string[] = [];
      const { stdout, stderr } = await awaitExec(`SANITY_AUTH_TOKEN=${process.env.SANITY_PERSONAL_TOKEN!} sanity dataset list`);

      if (stderr) {
        console.log(`stderr: ${stderr}`);
      }

      if (stdout) {
        console.log(`${stdout}`);
        datasets = sift(stdout.split("\n"));
      }

      return response.status(200).send({ data: datasets });
    } catch (error) {
      console.log(error);
    }
  }

  public async getBackups({ response }: HttpContextContract) {
    try {
      const files = await getFilesFromBucket(process.env.BUCKET!);
      return response.status(200).send({ data: files?.data ?? [] });
    } catch (error) {
      console.log(error);
    }
  }

  public async uploadBackup({ response }: HttpContextContract) {
    try {
      const fileName = "production_2023-08-04T01/54/25.648+08/00.tar.gz";

      const uploadResponse = await uploadFileToSpacesBucket(
        fileName,
        process.env.BUCKET!,
        fileName,
        `application/gzip`
      );

      return response.status(200).send({ data: uploadResponse });
    } catch (error) {
      console.log(error);
    }
  }
}
