import type { HttpContextContract } from "@ioc:Adonis/Core/HttpContext";
import axios from "axios";
import { isEmpty, first, last } from "radash";
import { makeSafe<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, q } from "groqd";
import { createClient } from "@sanity/client";
import type { PartialDeep } from "type-fest";
import escape from "lodash.escape";

type ConnectionEdges = {
  edges: Array<{ node: unknown }>;
};

type ConnectionNodes = {
  nodes: Array<unknown>;
};

export function flattenConnection<
  ConnectionGeneric extends
    | PartialDeep<ConnectionEdges, { recurseIntoArrays: true }>
    | PartialDeep<ConnectionNodes, { recurseIntoArrays: true }>
    | ConnectionEdges
    | ConnectionNodes
>(
  connection?: ConnectionGeneric
): ConnectionGeneric extends
  | {
      edges: Array<{ node: infer ConnectionBaseType }>;
    }
  | {
      nodes: Array<infer ConnectionBaseType>;
    }
  ? // if it's not a PartialDeep, then return the infered type
    ConnectionBaseType[]
  : ConnectionGeneric extends
      | PartialDeep<
          { edges: { node: Array<infer ConnectionBaseType> } },
          { recurseIntoArrays: true }
        >
      | PartialDeep<
          {
            nodes: Array<infer ConnectionBaseType>;
          },
          { recurseIntoArrays: true }
        >
  ? // if it is a PartialDeep, return a PartialDeep inferred type
    PartialDeep<ConnectionBaseType[], { recurseIntoArrays: true }>
  : never {
  if (!connection) {
    const noConnectionErr = `flattenConnection(): needs a 'connection' to flatten, but received '${
      connection ?? ""
    }' instead.`;
    console.error(noConnectionErr + ` Returning an empty array`);
    // @ts-expect-error We don't want to crash prod, so return an empty array
    return [];
  }

  if ("nodes" in connection) {
    // @ts-ignore
    return connection.nodes;
  }

  if ("edges" in connection && Array.isArray(connection.edges)) {
    // @ts-expect-error return type is failing
    return connection.edges.map((edge) => {
      if (!edge?.node) {
        throw new Error(
          "flattenConnection(): Connection edges must contain nodes"
        );
      }
      return edge.node;
    }) as Array<unknown>;
  }

  // @ts-expect-error We don't want to crash prod, so return an empty array
  return [];
}

function generateReviewsXML(
  products: { title: string; slug: string }[],
  reviews: any
) {
  const webEnv = getWebEnv()!;

  return `<?xml version="1.0" encoding="UTF-8"?>
  <feed xmlns:vc="http://www.w3.org/2007/XMLSchema-versioning"
   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
   xsi:noNamespaceSchemaLocation=
   "http://www.google.com/shopping/reviews/schema/product/2.3/product_reviews.xsd">
      <version>2.3</version>
      <aggregator>
          <name>Senders Reviews</name>
      </aggregator>
      <publisher>
          <name>Casefinite</name>
          <favicon>https://www.casefinite.jp/favicon.ico</favicon>
      </publisher>
      <reviews>
     ${reviews
       .map(
         ({
           id,
           name,
           title,
           score,
           created_at,
           product,
           variants,
           content,
         }: any) => {
           const variant = variants?.length > 0 ? variants[0] : null;
           if (!variant) {
             return ``;
           }

           return `<review>
         <review_id>${id}</review_id>
         <reviewer>
             <name>${name ?? ""}</name>
         </reviewer>
         <review_timestamp>${created_at}</review_timestamp>
         ${isEmpty(title) ? `` : `<title>${escape(title)}</title>`}
         <content>${escape(content)}</content>
         <review_url type="singleton">${webEnv.publicUrl}/products/${
             products.find(
               (p) => p.title.toLowerCase() == product?.name?.toLowerCase()
             )?.slug
           }?review=${id}</review_url>
         <ratings>
            <overall min="1" max="5">${score}</overall>
         </ratings>
         ${
           variant != null
             ? `<products>
         <product>
             <product_ids>
                 <gtins>
                     <gtin>${variant.barcode}</gtin>
                 </gtins>
                 <mpns>
                     <mpn>${variant.custom_id}</mpn>
                 </mpns>
                 <skus>
                     <sku>${variant.sku}</sku>
                 </skus>
                 <brands>
                     <brand>CASEFINITE</brand>
                 </brands>
             </product_ids>
             <product_name>${product?.name}</product_name>
             <product_url>${webEnv.publicUrl}/products/${
                 products.find(
                   (p) => p.title.toLowerCase() == product?.name?.toLowerCase()
                 )?.slug
               }</product_url>
          </product>
      </products>`
             : ``
         }
     </review>`;
         }
       )
       .join("")}
       </reviews>
    </feed>
 `;
}

function generateShopProductsXML(products: any) {
  const webEnv = getWebEnv()!;

  return `<?xml version="1.0" encoding="UTF-8"?>
  <rss xmlns:g="http://base.google.com/ns/1.0" version="2.0">
  	<channel>
  		<title>CASEFINITE</title>
  		<link>${webEnv.publicUrl}</link>
  		<description>CASEFINITE</description>

      ${products.map(({ node }) => {
        const variants = flattenConnection(node.variants);
        const variant: any = first(variants);
        const id = last(node.id.split("/"));
        const variantId = last(variant?.id?.split("/") ?? []);

        return `<item>
          <g:id>shopify_JP_${id}_${variantId}</g:id>
          <g:title>${node.title}</g:title>
          <g:description>${node.seo.description}</g:description>
          <g:link>${webEnv.publicUrl}/products/${node.handle}</g:link>
          <g:image_link>${node.featuredImage?.url}</g:image_link>
          <g:condition>new</g:condition>
          <g:availability>in stock</g:availability>
          <g:price>${
            // @ts-ignore
            node.priceRangeV2.minVariantPrice.amount
          } ${
          // @ts-ignore
          node.priceRangeV2.minVariantPrice.currencyCode
        }</g:price>
          <g:shipping>
          	<g:country>JP</g:country>
          	<g:service>Standard Free Shipping</g:service>
          	<g:price>0 JPY</g:price>
          </g:shipping>

          <g:gtin>${variant?.barcode}</g:gtin>
          <g:brand>CASEFINITE</g:brand>
          <g:sku>${variant?.sku}</g:sku>
        </item>`;
      })}
  	</channel>
  </rss>`;
}

const getWebEnv = (environment = process.env.STORE_TYPE ?? "JAPAN") => {
  switch (environment) {
    case "JAPAN":
      return {
        storeType: environment,
        currency: "JPY",
        currencySymbol: "¥",
        acceptedLanguage: "JA",
        defaultCountry: "JP",
        isoCode: "ja-JP",
        hotjarId: "2257701",
        pixelId: "575639153168786",
        klaviyoPublicKey: "KnKmQw",
        googleAds: ["AW-10946930754", "AW-11456645171"],
        googleTagManager: "GTM-WN3JGS9",
        googleAnalytics: {
          name: "Casefinite",
          measurementIds: ["G-HB04Q2NWN6", "G-BFL4CJ5PR8"],
        },
        subscribeListId: "SmYRKz",
        footerListId: "SmYRKz",
        retailListId: "SdL9AH",
        nwIpRegister: {
          listId: "XCfLXr",
          script:
            "https://script.google.com/macros/s/AKfycbz7IlYXbYoQH7hx7_XJSDcvCtpPyEvQ1Z7jvTJXEJDgVYDPKTwsBnOpAUcsgyjv3x6OJw/exec",
        },
        reviewClientId: "fe83257e68302f842af0d1301516b7ed1fb7f09c5d7bc8caaf",
        reviewClientOrigin: "https://api.senders.jp/reviews/public",
        sendEmail: {
          from: "<EMAIL>",
          to: "<EMAIL>",
          senderCustomerService: {
            url: "https://api.senders.jp",
            id: "ecf3e8df-65e7-470b-a157-ad334fe8f2d2",
          },
        },
        sendBusinessEmail: {
          to: ["<EMAIL>", "<EMAIL>"],
          senderCustomerService: {
            url: "https://api.senders.jp",
            id: "5836e8a5-4848-4e64-91f8-4828d4d93aa2",
          },
        },
        publicUrl: "https://casefinite.jp",
      } as const;

    case "GLOBAL":
      return {
        storeType: environment,
        currency: "USD",
        currencySymbol: "$",
        acceptedLanguage: "EN",
        defaultCountry: "US",
        isoCode: "en-US",
        hotjarId: "3698969",
        pixelId: "1110735015959976",
        klaviyoPublicKey: "W8q24M",
        googleAds: ["AW-***********"],
        googleTagManager: "GTM-TFJC8SC",
        googleAnalytics: {
          name: "Casefinite Global",
          measurementIds: ["G-312214622"],
        },
        subscribeListId: "VfBJMZ",
        footerListId: "U6kRZe",
        retailListId: "U6kRZe",
        nwIpRegister: {
          listId: "U6kRZe",
          script:
            "https://script.google.com/macros/s/AKfycbz7IlYXbYoQH7hx7_XJSDcvCtpPyEvQ1Z7jvTJXEJDgVYDPKTwsBnOpAUcsgyjv3x6OJw/exec",
        },
        reviewClientId: "c6e7e0eb975cd28d4bf78ba5c854b3baf102ddd12785ceb033",
        reviewClientOrigin: "https://api.senders.jp/reviews/public",
        sendEmail: {
          from: "",
          to: "<EMAIL>",
          senderCustomerService: {
            url: "https://api.senders.jp",
            id: "c2ff8fe2-15e0-4ede-b990-9d64538d28aa",
          },
        },
        sendBusinessEmail: {
          to: ["<EMAIL>"],
          senderCustomerService: {
            url: "https://api.senders.jp",
            id: "8ce0f699-c9c0-4234-9fc0-5bf79e90a7cb",
          },
        },
        publicUrl: "https://casefinite.global",
      } as const;

    case "TAIWAN":
      return {
        storeType: environment,
        currency: "TWD",
        currencySymbol: "$",
        acceptedLanguage: "ZH_TW",
        defaultCountry: "TW",
        isoCode: "zh-TW",
        hotjarId: "3789006",
        pixelId: "896855048684087",
        klaviyoPublicKey: "ReSwm4",
        googleAds: [""],
        googleTagManager: "",
        googleAnalytics: {
          name: "Casefinite Taiwan",
          measurementIds: ["G-MQSJPJ90DW"],
        },
        subscribeListId: "VPDQWJ",
        footerListId: "TaxUpN",
        retailListId: "RAajbL",
        nwIpRegister: {
          listId: "RAajbL",
          script:
            "https://script.google.com/macros/s/AKfycbz7IlYXbYoQH7hx7_XJSDcvCtpPyEvQ1Z7jvTJXEJDgVYDPKTwsBnOpAUcsgyjv3x6OJw/exec",
        },
        reviewClientId: "2923580543bb240efd7dc41a3ddab195724af2da7a6bf25135",
        reviewClientOrigin: "https://api.senders.jp/reviews/public",
        sendEmail: {
          from: "<EMAIL>",
          to: "<EMAIL>",
          senderCustomerService: {
            url: "https://api.senders.jp",
            id: "77f54a52-a20b-42ec-a152-7f46aea2ca3b",
          },
        },
        sendBusinessEmail: {
          to: [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
          ],
          senderCustomerService: {
            url: "https://api.senders.jp",
            id: "5836e8a5-4848-4e64-91f8-4828d4d93aa2",
          },
        },
        publicUrl: "https://casefinite.tw",
      } as const;
  }
};

export default class FeedsController {
  public async reviews({ response }: HttpContextContract) {
    const webEnv = getWebEnv()!;
    const clientId = webEnv.reviewClientId;

    const dataset: any = [];
    const { data: reviewResponse } = await axios.get(
      `${webEnv.reviewClientOrigin}/app-clients/${clientId}/reviews?limit=100`
    );

    dataset.push(...reviewResponse.data);

    // loop until we reach last page
    if (reviewResponse.meta.current_page != reviewResponse.meta.last_page) {
      for (
        let index = reviewResponse.meta.current_page + 1;
        index <= reviewResponse.meta.last_page;
        index++
      ) {
        const { data: nestedResponse } = await axios.get(
          `${webEnv.reviewClientOrigin}/app-clients/${clientId}/reviews?limit=100&page=${index}`
        );

        dataset.push(...nestedResponse.data);
      }
    }

    const sanityClient = createClient({
      dataset: process.env.SANITY_PROJECT_DATASET, // need to always get latest
      projectId: process.env.SANITY_PROJECT_ID,
      useCdn: true,
      apiVersion: "2023-04-03",
    });

    // get all products first, to find slugs
    const products = await makeSafeQueryRunner(
      (query: string, params: Record<string, number | string> = {}) =>
        sanityClient.fetch(query, params)
    )(
      q("*")
        .filter(`_type == "product" && !(_id in path("drafts.**"))`)
        .grab$({
          slug: q("store.slug").grabOne("current", q.string()),
          title: q("store").grabOne("title", q.string()),
        })
    );

    const reviews = generateReviewsXML(products, dataset);

    return response
      .header("Content-Type", "text/xml;charset=utf-8")
      .ok(reviews);
  }

  public async shopProducts({ response }: HttpContextContract) {
    const queryString = (ids: number[] = []) => `query {
      products(first: 100, query: "${ids
        ?.map((id) => `(id:${id})`)
        .join(" OR ")}") {
          edges {
            node {
              id
              featuredImage {
                url
              }
              title
              handle
              seo {
                description
              }
              priceRangeV2 {
                minVariantPrice {
                  amount
                  currencyCode
                }
              }
              variants(first: 1) {
                edges {
                  node {
                    id,
                    title,
                    displayName,
                    sku,
                    price,
                    barcode,
                    image {
                      url
                    }
                  }
                }
              }
            }
          }
        }
      }`;

    const sanityClient = createClient({
      dataset: process.env.SANITY_PROJECT_DATASET, // need to always get latest
      projectId: process.env.SANITY_PROJECT_ID,
      useCdn: true,
      apiVersion: "2023-04-03",
    });

    // const feeds: number[] = (

    // )?.data?.feeds;

    // const feed = await sanityClient.fetch(`{
    //   "data": ${
    //     q("*")
    //       .filter('_type == "settings"')
    //       .grab({
    //         feeds: q("feeds").filter().deref().grabOne("store.id", q.number()),
    //       })
    //       .slice(0).query
    //   }
    // }`);

    const feeds = await makeSafeQueryRunner(
      (query: string, params: Record<string, number | string> = {}) =>
        sanityClient.fetch(query, params)
    )(
      q("*")
        .filter(`_type == "settings" && !(_id in path("drafts.**"))`)
        .grab$({
          feeds: q("feeds")
            .filter()
            .deref()
            .grabOne("store.id", q.number())
            .nullable(),
        })
        .slice(0)
    );

    if (feeds.feeds == null) {
      return response.ok({});
    }

    const data = await axios.post<any, any>(
      `${process.env.SHOPIFY_SHOP_DOMAIN}/admin/api/2023-10/graphql.json`,
      {
        query: queryString(feeds.feeds),
      },
      {
        headers: {
          "Content-Type": "application/json",
          "X-Shopify-Access-Token": process.env.SHOPIFY_ADMIN_ACCESS_TOKEN!,
        },
      }
    );

    const xml = generateShopProductsXML(
      data?.data?.data?.products?.edges ?? []
    );

    return response.header("Content-Type", "text/xml;charset=utf-8").ok(xml);
  }
}
