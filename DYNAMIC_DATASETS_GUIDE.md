# Dynamic Dataset Names - Configuration Guide

Since your dataset names follow a dynamic pattern and you have:
- `jp_playground` (development/testing)
- `jp_20250326` (older production)
- `jp_20250413` (production - usually 2nd latest)
- `jp_20250502` (staging - usually latest)

Here are the best strategies to configure your backup system:

## Configuration Strategies

### 1. Simple Pattern-Based (Easiest to implement)

**For production only (exclude playground and latest):**
```env
# This will backup all jp_ datasets except playground
BACKUP_DATASETS=jp_2025*
```

**For all JP datasets (including staging):**
```env
BACKUP_DATASETS=jp_*
```

**For specific datasets:**
```env
# Manually specify the production dataset
BACKUP_DATASETS=jp_20250413
```

### 2. Smart Selection (Advanced - requires manual setup)

Since the smart selection feature has some technical issues, here's a manual approach:

**Create a script to identify production dataset:**
```bash
# Create a simple script to get the second-latest dataset
#!/bin/bash
DATASETS=$(SANITY_AUTH_TOKEN=$SANITY_PERSONAL_TOKEN sanity dataset list | grep "jp_2025" | grep -v playground | sort -r)
PRODUCTION=$(echo "$DATASETS" | sed -n '2p')
echo "Production dataset: $PRODUCTION"
```

**Update your .env periodically:**
```env
# Update this when you create new datasets
BACKUP_DATASETS=jp_20250413
```

### 3. Recommended Configuration for Your Use Case

**Option A: Production Only (Safest)**
```env
# Manually update this when you create new datasets
BACKUP_DATASETS=jp_20250413
BACKUP_NOTIFICATION_EMAILS=<EMAIL>
```

**Option B: All JP datasets except playground**
```env
# This will backup all jp_2025* datasets (excludes playground)
BACKUP_DATASETS=jp_2025*
BACKUP_NOTIFICATION_EMAILS=<EMAIL>
```

**Option C: All JP datasets (including staging)**
```env
# This will backup everything starting with jp_
BACKUP_DATASETS=jp_*
BACKUP_NOTIFICATION_EMAILS=<EMAIL>
```

## Testing Your Configuration

### 1. Check what will be backed up:
```bash
node ace list:backup_datasets
```

### 2. Test with different patterns:
```bash
# Test JP datasets only
node ace list:backup_datasets --pattern="jp_*"

# Test specific year
node ace list:backup_datasets --pattern="jp_2025*"

# Test multiple patterns
node ace list:backup_datasets --pattern="jp_*,testground_*"
```

### 3. Test a manual backup:
```bash
# Backup a specific dataset
node ace backup:dataset jp_playground_20250218 --keep-local

# This will show you the full backup process without uploading
```

## Advanced Patterns

### Date-based patterns:
```env
# All datasets from 2025
BACKUP_DATASETS=*2025*

# All JP datasets from January 2025
BACKUP_DATASETS=jp_202501*

# All datasets ending with specific dates
BACKUP_DATASETS=*20250101,*20250102
```

### Complex combinations:
```env
# JP datasets + specific others
BACKUP_DATASETS=jp_*,production,staging_live

# Multiple prefixes
BACKUP_DATASETS=jp_*,us_*,eu_*
```

## Automation Considerations

### For Production:
1. **Use broad patterns** like `jp_*` to automatically include new datasets
2. **Monitor backup logs** to ensure new datasets are being picked up
3. **Set up alerts** if no datasets match your patterns

### For Development:
1. **Use specific patterns** like `jp_2025*` to limit scope
2. **Test patterns regularly** with `node ace list:backup_datasets`
3. **Use the `--pattern` flag** to test before updating your .env

## Your Next Steps

1. **Update your .env file:**
   ```env
   BACKUP_DATASETS=jp_*
   BACKUP_NOTIFICATION_EMAILS=<EMAIL>
   ```

2. **Test the configuration:**
   ```bash
   node ace list:backup_datasets
   ```

3. **Start the scheduler:**
   ```bash
   node ace scheduler:run
   ```

4. **Monitor the first backup** (runs daily at 2:00 AM)

## Pattern Matching Examples

Given datasets: `jp_20250101`, `jp_20250102`, `jp_playground_20250218`, `testground_dataset_20240824`

| Pattern | Matches |
|---------|---------|
| `jp_*` | `jp_20250101`, `jp_20250102`, `jp_playground_20250218` |
| `jp_2025*` | `jp_20250101`, `jp_20250102`, `jp_playground_20250218` |
| `jp_202501*` | `jp_20250101`, `jp_20250102` |
| `*playground*` | `jp_playground_20250218` |
| `testground_*` | `testground_dataset_20240824` |
| `jp_*,testground_*` | All datasets |

This flexible system will automatically pick up new datasets as you create them, without needing to update your configuration!
