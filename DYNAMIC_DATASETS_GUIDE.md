# Dynamic Dataset Names - Configuration Guide

Since your dataset names follow a dynamic pattern like `jp_20250101`, here are the best strategies to configure your backup system:

## Current Available Datasets
Based on your Sanity project, you currently have:
- `testground_dataset_20240824`
- `jp_playground_20250218`

## Configuration Strategies

### 1. Pattern-Based (Recommended)

**For all JP datasets:**
```env
BACKUP_DATASETS=jp_*
```
This will backup any dataset starting with "jp_"

**For specific date ranges:**
```env
BACKUP_DATASETS=jp_2025*
```
This will backup JP datasets from 2025

**For multiple patterns:**
```env
BACKUP_DATASETS=jp_*,testground_*
```
This will backup both JP and testground datasets

### 2. Most Recent Dataset Only

If you only want to backup the most recent dataset, you could create a more sophisticated pattern or use a custom script. For now, you can:

1. **Check which datasets match your pattern:**
   ```bash
   node ace list:backup_datasets --pattern="jp_*"
   ```

2. **Update your .env file periodically** with the specific dataset name:
   ```env
   BACKUP_DATASETS=jp_20250101
   ```

### 3. Recommended Configuration for Your Use Case

Based on your dataset naming pattern, I recommend:

```env
# Backup all JP datasets (current and future)
BACKUP_DATASETS=jp_*

# Your notification email
BACKUP_NOTIFICATION_EMAILS=<EMAIL>
```

## Testing Your Configuration

### 1. Check what will be backed up:
```bash
node ace list:backup_datasets
```

### 2. Test with different patterns:
```bash
# Test JP datasets only
node ace list:backup_datasets --pattern="jp_*"

# Test specific year
node ace list:backup_datasets --pattern="jp_2025*"

# Test multiple patterns
node ace list:backup_datasets --pattern="jp_*,testground_*"
```

### 3. Test a manual backup:
```bash
# Backup a specific dataset
node ace backup:dataset jp_playground_20250218 --keep-local

# This will show you the full backup process without uploading
```

## Advanced Patterns

### Date-based patterns:
```env
# All datasets from 2025
BACKUP_DATASETS=*2025*

# All JP datasets from January 2025
BACKUP_DATASETS=jp_202501*

# All datasets ending with specific dates
BACKUP_DATASETS=*20250101,*20250102
```

### Complex combinations:
```env
# JP datasets + specific others
BACKUP_DATASETS=jp_*,production,staging_live

# Multiple prefixes
BACKUP_DATASETS=jp_*,us_*,eu_*
```

## Automation Considerations

### For Production:
1. **Use broad patterns** like `jp_*` to automatically include new datasets
2. **Monitor backup logs** to ensure new datasets are being picked up
3. **Set up alerts** if no datasets match your patterns

### For Development:
1. **Use specific patterns** like `jp_2025*` to limit scope
2. **Test patterns regularly** with `node ace list:backup_datasets`
3. **Use the `--pattern` flag** to test before updating your .env

## Your Next Steps

1. **Update your .env file:**
   ```env
   BACKUP_DATASETS=jp_*
   BACKUP_NOTIFICATION_EMAILS=<EMAIL>
   ```

2. **Test the configuration:**
   ```bash
   node ace list:backup_datasets
   ```

3. **Start the scheduler:**
   ```bash
   node ace scheduler:run
   ```

4. **Monitor the first backup** (runs daily at 2:00 AM)

## Pattern Matching Examples

Given datasets: `jp_20250101`, `jp_20250102`, `jp_playground_20250218`, `testground_dataset_20240824`

| Pattern | Matches |
|---------|---------|
| `jp_*` | `jp_20250101`, `jp_20250102`, `jp_playground_20250218` |
| `jp_2025*` | `jp_20250101`, `jp_20250102`, `jp_playground_20250218` |
| `jp_202501*` | `jp_20250101`, `jp_20250102` |
| `*playground*` | `jp_playground_20250218` |
| `testground_*` | `testground_dataset_20240824` |
| `jp_*,testground_*` | All datasets |

This flexible system will automatically pick up new datasets as you create them, without needing to update your configuration!
