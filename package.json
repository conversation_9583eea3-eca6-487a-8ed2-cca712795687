{"name": "sanity-hotswap-adonisjs", "version": "1.0.0", "private": true, "scripts": {"dev": "node ace serve --watch", "build": "node ace build --production", "start": "node server.js", "test": "node ace test"}, "devDependencies": {"@adonisjs/assembler": "^5.9.5", "@japa/preset-adonis": "^1.2.0", "@japa/runner": "^2.5.1", "@types/proxy-addr": "^2.0.0", "@types/source-map-support": "^0.5.6", "adonis-preset-ts": "^2.1.0", "pino-pretty": "^10.2.0", "typescript": "^5.5.2", "youch": "^3.2.3", "youch-terminal": "^2.2.2"}, "dependencies": {"@adonisjs/core": "^5.8.0", "@adonisjs/lucid": "^18.4.0", "@adonisjs/repl": "^3.1.0", "@sanity/client": "^6.20.1", "@sanity/export": "^3.14.5", "@sendgrid/mail": "^7.7.0", "aws-sdk": "^2.1430.0", "axios": "^1.7.2", "groqd": "^0.15.11", "lodash.escape": "^4.0.1", "luxon": "^3.3.0", "mysql2": "^3.5.2", "proxy-addr": "^2.0.7", "radash": "^11.0.0", "reflect-metadata": "^0.1.13", "sanity": "^3.14.5", "source-map-support": "^0.5.21", "type-fest": "^4.20.1"}}