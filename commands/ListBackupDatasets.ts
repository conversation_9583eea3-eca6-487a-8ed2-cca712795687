import { BaseCommand, flags } from '@adonisjs/core/build/standalone'
import BackupService from '../app/Services/BackupService'
import Env from '@ioc:Adonis/Core/Env'

export default class ListBackupDatasets extends BaseCommand {
  /**
   * Command name is used to run the command
   */
  public static commandName = 'list:backup_datasets'

  /**
   * Command description is displayed in the "help" output
   */
  public static description = 'List datasets that will be backed up based on current configuration'

  /**
   * Define command flags
   */
  @flags.string({ description: 'Override BACKUP_DATASETS pattern for testing' })
  public pattern: string

  public static settings = {
    /**
     * Set the following value to true, if you want to load the application
     * before running the command. Don't forget to call `node ace generate:manifest`
     * afterwards.
     */
    loadApp: true,

    /**
     * Set the following value to true, if you want this command to keep running until
     * you manually decide to exit the process. Don't forget to call
     * `node ace generate:manifest` afterwards.
     */
    stayAlive: false,
  }

  public async run() {
    const backupService = new BackupService()

    try {
      // Get all available datasets
      this.logger.info('Fetching available datasets...')
      const allDatasets = await backupService.getAvailableDatasets()

      if (allDatasets.length === 0) {
        this.logger.warn('No datasets found in your Sanity project')
        return
      }

      this.logger.info(`\nAll available datasets (${allDatasets.length}):`)
      allDatasets.forEach(dataset => this.logger.info(`  📁 ${dataset}`))

      // Get backup patterns
      const patternsEnv = this.pattern || Env.get('BACKUP_DATASETS', 'production:jp_')
      const patterns = patternsEnv.split(',').map((pattern: string) => pattern.trim())

      this.logger.info(`\nBackup configuration: ${patterns.join(', ')}`)

      // Find matching datasets
      const datasetsToBackup: string[] = []

      for (const pattern of patterns) {
        if (pattern.startsWith('production:')) {
          // Smart production selection
          const prefix = pattern.replace('production:', '') || 'jp_'
          const productionDataset = await backupService.getProductionDataset({
            prefix: prefix,
            excludeLatest: true,
            excludePlayground: true
          })

          this.logger.info(`\nSmart selection "production:${String(prefix)}":`)
          if (productionDataset) {
            datasetsToBackup.push(productionDataset)
            this.logger.info(`  🏭 Production: ${productionDataset}`)
          } else {
            this.logger.warn('  ❌ No production dataset found')
          }

        } else if (pattern.startsWith('staging:')) {
          // Smart staging selection
          const prefix = pattern.replace('staging:', '') || 'jp_'
          const stagingDataset = await backupService.getStagingDataset({
            prefix: prefix,
            excludePlayground: true
          })

          this.logger.info(`\nSmart selection "staging:${String(prefix)}":`)
          if (stagingDataset) {
            datasetsToBackup.push(stagingDataset)
            this.logger.info(`  🚀 Staging: ${stagingDataset}`)
          } else {
            this.logger.warn('  ❌ No staging dataset found')
          }

        } else if (pattern.includes('*')) {
          // Pattern matching
          const regex = new RegExp('^' + pattern.replace(/\*/g, '.*') + '$')
          const matchingDatasets = allDatasets.filter(dataset => regex.test(dataset))
          datasetsToBackup.push(...matchingDatasets)

          this.logger.info(`\nPattern "${pattern}" matches:`)
          if (matchingDatasets.length === 0) {
            this.logger.warn('  ❌ No matches found')
          } else {
            matchingDatasets.forEach(dataset => this.logger.info(`  ✅ ${dataset}`))
          }
        } else {
          // Exact match
          if (allDatasets.includes(pattern)) {
            datasetsToBackup.push(pattern)
            this.logger.info(`\nExact match "${pattern}": ✅ Found`)
          } else {
            this.logger.warn(`\nExact match "${pattern}": ❌ Not found`)
          }
        }
      }

      // Remove duplicates and show final result
      const uniqueDatasets = [...new Set(datasetsToBackup)]

      this.logger.info(`\n📋 Final backup list (${uniqueDatasets.length} datasets):`)
      if (uniqueDatasets.length === 0) {
        this.logger.warn('  ❌ No datasets will be backed up with current configuration')
        this.logger.info('\n💡 Suggestions:')
        this.logger.info('  - Check your BACKUP_DATASETS environment variable')
        this.logger.info('  - Use patterns like "jp_*" to match multiple datasets')
        this.logger.info('  - Use exact names separated by commas')
      } else {
        uniqueDatasets.forEach(dataset => this.logger.info(`  🎯 ${dataset}`))
      }

    } catch (error) {
      this.logger.error('Failed to list backup datasets:', error)
      process.exit(1)
    }
  }
}
