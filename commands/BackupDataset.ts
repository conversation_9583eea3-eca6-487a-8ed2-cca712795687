import { BaseCommand, args, flags } from '@adonisjs/core/build/standalone'
import BackupService from '../app/Services/BackupService'

export default class BackupDataset extends BaseCommand {
  /**
   * Command name is used to run the command
   */
  public static commandName = 'backup:dataset'

  /**
   * Command description is displayed in the "help" output
   */
  public static description = 'Backup a Sanity dataset to cloud storage'

  /**
   * Define command arguments
   */
  @args.string({ description: 'Name of the dataset to backup', required: false })
  public dataset: string

  /**
   * Define command flags
   */
  @flags.boolean({ description: 'Include assets in the backup' })
  public assets: boolean = true

  @flags.boolean({ description: 'Include drafts in the backup' })
  public drafts: boolean = true

  @flags.boolean({ description: 'Keep local backup file after upload' })
  public keepLocal: boolean = false

  @flags.array({ description: 'Email addresses to notify (comma-separated)' })
  public emails: string[] = []

  public static settings = {
    /**
     * Set the following value to true, if you want to load the application
     * before running the command. Don't forget to call `node ace generate:manifest`
     * afterwards.
     */
    loadApp: true,

    /**
     * Set the following value to true, if you want this command to keep running until
     * you manually decide to exit the process. Don't forget to call
     * `node ace generate:manifest` afterwards.
     */
    stayAlive: false,
  }

  public async run() {
    this.logger.info(`Starting backup for dataset: ${this.dataset}`)

    const backupService = new BackupService()

    try {
      // Show available datasets if none specified
      if (!this.dataset) {
        this.logger.info('Getting available datasets...')
        const datasets = await backupService.getAvailableDatasets()

        if (datasets.length === 0) {
          this.logger.error('No datasets found. Please check your Sanity configuration.')
          return
        }

        this.logger.info('Available datasets:')
        datasets.forEach(dataset => this.logger.info(`  - ${dataset}`))
        this.logger.info('\nUsage: node ace backup:dataset <dataset-name>')
        return
      }

      // Perform the backup
      const result = await backupService.exportDataset({
        dataset: this.dataset,
        includeAssets: this.assets,
        includeDrafts: this.drafts,
        notificationEmails: this.emails.length > 0 ? this.emails : undefined,
        cleanupLocalFile: !this.keepLocal,
      })

      if (result.success) {
        this.logger.success(`✅ Backup completed successfully!`)
        this.logger.info(`   Dataset: ${this.dataset}`)
        this.logger.info(`   File: ${result.fileName}`)
        this.logger.info(`   Upload URL: ${result.uploadUrl}`)

        if (this.keepLocal) {
          this.logger.info(`   Local file preserved: ${result.fileName}`)
        }
      } else {
        this.logger.error(`❌ Backup failed: ${result.error}`)
        process.exit(1)
      }

    } catch (error) {
      this.logger.error('Backup command failed:', error)
      process.exit(1)
    }
  }
}
