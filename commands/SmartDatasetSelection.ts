import { BaseCommand, flags } from '@adonisjs/core/build/standalone'
import BackupService from '../app/Services/BackupService'

export default class SmartDatasetSelection extends BaseCommand {
  /**
   * Command name is used to run the command
   */
  public static commandName = 'smart:dataset_selection'

  /**
   * Command description is displayed in the "help" output
   */
  public static description = 'Test smart dataset selection (production/staging detection)'

  /**
   * Define command flags
   */
  @flags.string({ description: 'Dataset prefix to filter by', alias: 'p' })
  public prefix: string = 'jp_'

  public static settings = {
    /**
     * Set the following value to true, if you want to load the application
     * before running the command. Don't forget to call `node ace generate:manifest`
     * afterwards.
     */
    loadApp: true,

    /**
     * Set the following value to true, if you want this command to keep running until
     * you manually decide to exit the process. Don't forget to call
     * `node ace generate:manifest` afterwards.
     */
    stayAlive: false,
  }

  public async run() {
    const backupService = new BackupService()

    try {
      this.logger.info(`🔍 Smart Dataset Selection Analysis (prefix: ${this.prefix})`)
      this.logger.info('=' .repeat(60))

      // Get all available datasets
      const allDatasets = await backupService.getAvailableDatasets()
      this.logger.info(`\n📁 All available datasets (${allDatasets.length}):`)
      allDatasets.forEach(dataset => this.logger.info(`   ${dataset}`))

      // Filter by prefix
      const prefixDatasets = allDatasets.filter(dataset => dataset.startsWith(this.prefix))
      this.logger.info(`\n🎯 Datasets with prefix "${this.prefix}" (${prefixDatasets.length}):`)

      if (prefixDatasets.length === 0) {
        this.logger.warn('   ❌ No datasets found with this prefix')
        return
      }

      // Sort by date to show the logic
      const sortedDatasets = prefixDatasets
        .filter(dataset => !dataset.includes('playground'))
        .sort((a, b) => {
          const dateA = this.extractDate(a)
          const dateB = this.extractDate(b)
          if (dateA && dateB) {
            return dateB.localeCompare(dateA) // Newest first
          }
          return b.localeCompare(a)
        })

      this.logger.info('\n📅 Sorted datasets (newest first, excluding playground):')
      sortedDatasets.forEach((dataset, index) => {
        const date = this.extractDate(dataset)
        const label = index === 0 ? '🚀 STAGING (latest)' :
                     index === 1 ? '🏭 PRODUCTION (2nd latest)' :
                     '📦 OLDER'
        this.logger.info(`   ${index + 1}. ${dataset} ${date ? `(${date})` : ''} - ${label}`)
      })

      // Test smart selection
      this.logger.info('\n🤖 Smart Selection Results:')

      const productionDataset = await backupService.getProductionDataset({
        prefix: this.prefix,
        excludeLatest: true,
        excludePlayground: true
      })

      const stagingDataset = await backupService.getStagingDataset({
        prefix: this.prefix,
        excludePlayground: true
      })

      if (productionDataset) {
        this.logger.info(`   🏭 Production: ${productionDataset}`)
      } else {
        this.logger.warn('   🏭 Production: ❌ Not found')
      }

      if (stagingDataset) {
        this.logger.info(`   🚀 Staging: ${stagingDataset}`)
      } else {
        this.logger.warn('   🚀 Staging: ❌ Not found')
      }

      // Show what would be backed up with different configurations
      this.logger.info('\n⚙️  Configuration Examples:')
      this.logger.info(`   BACKUP_DATASETS=production:${this.prefix} → ${productionDataset || 'None'}`)
      this.logger.info(`   BACKUP_DATASETS=staging:${this.prefix} → ${stagingDataset || 'None'}`)
      this.logger.info(`   BACKUP_DATASETS=production:${this.prefix},staging:${this.prefix} → ${[productionDataset, stagingDataset].filter(Boolean).join(', ') || 'None'}`)

    } catch (error) {
      this.logger.error('Smart selection analysis failed:', error)
      process.exit(1)
    }
  }

  private extractDate(datasetName: string): string | null {
    const dateMatch = datasetName.match(/(\d{8})/)
    if (dateMatch) {
      const date = dateMatch[1]
      return `${date.slice(0,4)}-${date.slice(4,6)}-${date.slice(6,8)}`
    }
    return null
  }
}
