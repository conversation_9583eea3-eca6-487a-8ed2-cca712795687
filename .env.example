PORT=3333
HOST=0.0.0.0
NODE_ENV=development
APP_KEY=94ch4uN8SMWceI07jEoaeat8dMSOdJum
DRIVE_DISK=local
DB_CONNECTION=mysql
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=lucid
MYSQL_PASSWORD=
MYSQL_DB_NAME=lucid

# Sanity Configuration
SANITY_PROJECT_ID=your_project_id
SANITY_PERSONAL_TOKEN=your_personal_token

# Cloud Storage Configuration (DigitalOcean Spaces)
BUCKET=your_bucket_name
REGION=your_region
SPACES_KEY=your_spaces_key
SPACES_SECRET=your_spaces_secret

# Email Configuration
SENDGRID_API_KEY=your_sendgrid_api_key
DEFAULT_SENDGRID_MAIL=<EMAIL>

# Backup Configuration
# For dynamic dataset names like jp_20250413, jp_20250502, etc.
# Choose one of these strategies:

# Option 1: Backup specific production dataset (update manually when needed)
# BACKUP_DATASETS=jp_20250413

# Option 2: Backup all jp_ datasets from 2025 (excludes playground)
# BACKUP_DATASETS=jp_2025*

# Option 3: Backup all jp_ datasets (includes staging and playground)
BACKUP_DATASETS=jp_*

BACKUP_NOTIFICATION_EMAILS=<EMAIL>,<EMAIL>

# Security
SECRET=your_secret_key
